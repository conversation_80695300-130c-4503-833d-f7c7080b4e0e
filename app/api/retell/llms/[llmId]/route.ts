import { NextResponse } from 'next/server'
import { getRetellLLM, updateRetellLL<PERSON>, deleteRetellLLM } from '@/lib/retell-api'

// GET /api/retell/llms/[llmId] - Get LLM details
export async function GET(
  request: Request,
  { params }: { params: { llmId: string } }
) {
  try {
    const llm = await getRetellLLM(params.llmId)
    return NextResponse.json(llm)
  } catch (error) {
    console.error('Error fetching LLM:', error)
    return NextResponse.json(
      { error: 'Failed to fetch LLM' },
      { status: 500 }
    )
  }
}

// PUT /api/retell/llms/[llmId] - Update LLM
export async function PUT(
  request: Request,
  { params }: { params: { llmId: string } }
) {
  try {
    const body = await request.json()
    const updatedLLM = await updateRetellLLM(params.llmId, body)
    return NextResponse.json(updatedLLM)
  } catch (error) {
    console.error('Error updating LLM:', error)
    return NextResponse.json(
      { error: 'Failed to update LLM' },
      { status: 500 }
    )
  }
}

// DELETE /api/retell/llms/[llmId] - Delete LLM
export async function DELETE(
  request: Request,
  { params }: { params: { llmId: string } }
) {
  try {
    await deleteRetellLLM(params.llmId)
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error deleting LLM:', error)
    return NextResponse.json(
      { error: 'Failed to delete LLM' },
      { status: 500 }
    )
  }
}
