"use client";

import React, { useState, useEffect, Suspense } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  ArrowLeft,
  Check,
  ChevronRight,
  Sparkles,
  User,
  Mic,
  Brain,
  Database,
  GraduationCap,
  Rocket
} from "lucide-react";
import { AgentTemplate } from '@/lib/agent-templates';
import TemplateSelectionStep from '@/components/agent-builder/TemplateSelectionStep';
import BasicInfoStep from '@/components/agent-builder/BasicInfoStep';
import VoiceSelectionStep from '@/components/agent-builder/VoiceSelectionStep';
import SmartBehaviorStep from '@/components/agent-builder/SmartBehaviorStep';
import KnowledgeBaseStep from '@/components/agent-builder/KnowledgeBaseStep';
import TrainingStep from '@/components/agent-builder/TrainingStep';
import AgentCreationStep from '@/components/agent-builder/AgentCreationStep';
import { useToast } from "@/hooks/use-toast";

const STEPS = [
  {
    id: 1,
    name: 'Template',
    title: 'Choose Template',
    description: 'Select a starting point',
    icon: Sparkles,
    component: 'template'
  },
  {
    id: 2,
    name: 'Basic Info',
    title: 'Basic Information',
    description: 'Name and description',
    icon: User,
    component: 'basic'
  },
  {
    id: 3,
    name: 'Voice',
    title: 'Voice Selection',
    description: 'Choose agent voice',
    icon: Mic,
    component: 'voice'
  },
  {
    id: 4,
    name: 'Behavior',
    title: 'Agent Behavior',
    description: 'Configure personality',
    icon: Brain,
    component: 'behavior'
  },
  {
    id: 5,
    name: 'Knowledge',
    title: 'Knowledge Base',
    description: 'Add information sources',
    icon: Database,
    component: 'knowledge'
  },
  {
    id: 6,
    name: 'Training',
    title: 'Training Examples',
    description: 'Provide examples',
    icon: GraduationCap,
    component: 'training'
  },
  {
    id: 7,
    name: 'Update',
    title: 'Update Agent',
    description: 'Save changes',
    icon: Rocket,
    component: 'creation'
  }
];

interface Voice {
  voice_id: string;
  voice_name: string;
  provider: string;
  accent?: string;
  gender?: string;
  age?: string;
  avatar_url: string;
  preview_audio_url: string;
}

interface Agent {
  agent_id: string;
  agent_name: string;
  voice_id: string;
  voice_model?: string;
  language?: string;
  response_engine?: {
    type: string;
    llm_id: string;
  };
  webhook_url?: string;
  created_at?: string;
  last_modification_timestamp?: string;
  description?: string;
  status?: "active" | "inactive";
  knowledgeBases?: number;
  totalCalls?: number;
  voice?: string;
  phone?: string;
  editedBy?: string;
  type?: string;
  behavior?: string;
}

function EditAgentContent() {
  const router = useRouter();
  const params = useParams();
  const { toast } = useToast();
  const agentId = params.agentId as string;
  
  const [currentStep, setCurrentStep] = useState(1);
  const [selectedTemplate, setSelectedTemplate] = useState<AgentTemplate | null>(null);
  const [selectedVoice, setSelectedVoice] = useState<Voice | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [agentData, setAgentData] = useState<Agent | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    voiceId: '',
    behavior: '',
    knowledgeBase: [],
    trainingExamples: []
  });

  // Fetch agent data on mount
  useEffect(() => {
    const fetchAgentData = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/retell/agents/${agentId}`);
        
        if (!response.ok) {
          throw new Error('Failed to fetch agent data');
        }
        
        const agent = await response.json();
        setAgentData(agent);
        
        // Pre-populate form data
        setFormData({
          name: agent.agent_name || '',
          description: agent.description || '',
          voiceId: agent.voice_id || '',
          behavior: agent.behavior || '',
          knowledgeBase: [],
          trainingExamples: []
        });
        
        // Set selected voice if available
        if (agent.voice_id) {
          // You might want to fetch voice details here
          setSelectedVoice({
            voice_id: agent.voice_id,
            voice_name: agent.voice || 'Unknown Voice',
            provider: 'RetellAI',
            avatar_url: '',
            preview_audio_url: ''
          });
        }
        
      } catch (err) {
        console.error('Error fetching agent:', err);
        setError(err instanceof Error ? err.message : 'Failed to load agent');
        toast({
          title: "Error",
          description: "Failed to load agent data. Please try again.",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    if (agentId) {
      fetchAgentData();
    }
  }, [agentId, toast]);

  const currentStepData = STEPS.find(step => step.id === currentStep);
  
  // Calculate progress in multiples of 5%
  const calculateProgress = (step: number, totalSteps: number) => {
    const stepProgress = [15, 30, 45, 60, 75, 90, 100];
    return stepProgress[step - 1] || 0;
  };
  
  const progress = calculateProgress(currentStep, STEPS.length);

  const handleNext = () => {
    if (currentStep < STEPS.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleStepClick = (stepId: number) => {
    // Allow navigation to previous steps or current step
    if (stepId <= currentStep) {
      setCurrentStep(stepId);
    }
  };

  const handleVoiceSelect = (voice: Voice) => {
    setSelectedVoice(voice);
    setFormData(prev => ({ ...prev, voiceId: voice.voice_id }));
  };

  const handleFormDataChange = (data: { name: string; description: string }) => {
    setFormData(prev => ({ ...prev, ...data }));
  };

  const handleUpdateAgent = (updatedAgentData: any) => {
    // Handle agent update success
    console.log('Agent updated:', updatedAgentData);
    toast({
      title: "Agent Updated",
      description: "Your AI agent has been successfully updated.",
      variant: "default",
    });
    router.push('/agent-builder');
  };

  const handleCancel = () => {
    router.push('/agent-builder');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-foreground">Loading agent data...</p>
        </div>
      </div>
    );
  }

  if (error || !agentData) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-500 mb-4">{error || 'Agent not found'}</p>
          <Button onClick={() => router.push('/agent-builder')}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Agent Builder
          </Button>
        </div>
      </div>
    );
  }

  const renderStepContent = () => {
    switch (currentStepData?.component) {
      case 'template':
        return (
          <TemplateSelectionStep
            onSelectTemplate={setSelectedTemplate}
            onNext={handleNext}
            onBack={handleBack}
            selectedTemplate={selectedTemplate}
          />
        );
      case 'basic':
        return (
          <BasicInfoStep
            selectedTemplate={selectedTemplate}
            formData={{ name: formData.name, description: formData.description }}
            onFormDataChange={handleFormDataChange}
            onNext={handleNext}
            onBack={handleBack}
          />
        );
      case 'voice':
        return (
          <VoiceSelectionStep
            selectedTemplate={selectedTemplate}
            selectedVoice={selectedVoice}
            onVoiceSelect={handleVoiceSelect}
            onNext={handleNext}
            onBack={handleBack}
          />
        );
      case 'behavior':
        return (
          <SmartBehaviorStep
            selectedTemplate={selectedTemplate}
            behavior={formData.behavior}
            onBehaviorChange={(behavior) => setFormData(prev => ({ ...prev, behavior }))}
            onNext={handleNext}
            onBack={handleBack}
          />
        );
      case 'knowledge':
        return (
          <KnowledgeBaseStep
            selectedTemplate={selectedTemplate}
            onNext={handleNext}
            onBack={handleBack}
          />
        );
      case 'training':
        return (
          <TrainingStep
            selectedTemplate={selectedTemplate}
            onNext={handleNext}
            onBack={handleBack}
          />
        );
      case 'creation':
        return (
          <AgentCreationStep
            agentData={{
              name: formData.name,
              description: formData.description,
              voiceId: formData.voiceId,
              behavior: formData.behavior,
              selectedTemplate,
              selectedVoice,
              knowledgeBaseSelections: formData.knowledgeBase,
              trainingExamples: formData.trainingExamples
            }}
            onSuccess={handleUpdateAgent}
            onBack={handleBack}
            isEditMode={true}
            existingAgentId={agentId}
          />
        );
      default:
        return (
          <div className="max-w-3xl mx-auto py-8">
            <h2 className="text-2xl font-bold text-foreground mb-4">{currentStepData?.title}</h2>
            <p className="text-muted-foreground mb-8">{currentStepData?.description}</p>
            <div className="flex justify-between">
              <Button variant="outline" onClick={handleBack}>
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back
              </Button>
              <Button onClick={handleNext}>
                Next Step
                <ChevronRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-background flex overflow-hidden">
      {/* Sidebar Navigation */}
      <div className="w-80 bg-card border-r border-border flex flex-col">
        {/* Header */}
        <div className="p-6 border-b border-border">
          <div className="flex items-center justify-between mb-4">
            <h1 className="text-xl font-bold text-foreground">Edit AI Agent</h1>
            <Button variant="ghost" size="sm" onClick={handleCancel}>
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </div>

          {/* Agent Info */}
          <div className="mb-4 p-3 bg-muted rounded-lg">
            <h3 className="font-medium text-sm">{agentData.agent_name}</h3>
            <p className="text-xs text-muted-foreground">{agentData.description}</p>
          </div>

          {/* Progress */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm text-muted-foreground">
              <span>Progress</span>
              <span>{progress}%</span>
            </div>
            <div className="w-full bg-muted rounded-full h-2">
              <div
                className="bg-primary h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress}%` }}
              />
            </div>
          </div>
        </div>

        {/* Steps */}
        <div className="flex-1 p-4 space-y-2">
          {STEPS.map((step) => {
            const Icon = step.icon;
            const isActive = step.id === currentStep;
            const isCompleted = step.id < currentStep;
            const isAccessible = step.id <= currentStep;

            return (
              <button
                key={step.id}
                onClick={() => handleStepClick(step.id)}
                disabled={!isAccessible}
                className={`w-full p-4 rounded-lg text-left transition-all duration-200 ${
                  isActive
                    ? 'bg-primary text-primary-foreground shadow-lg'
                    : isCompleted
                    ? 'bg-muted text-muted-foreground hover:bg-muted/80'
                    : isAccessible
                    ? 'bg-muted text-muted-foreground hover:bg-muted/80'
                    : 'bg-muted/50 text-muted-foreground/50 cursor-not-allowed'
                }`}
              >
                <div className="flex items-center space-x-3">
                  <div className={`flex-shrink-0 ${
                    isCompleted ? 'text-green-500' : isActive ? 'text-primary-foreground' : 'text-muted-foreground'
                  }`}>
                    {isCompleted ? (
                      <Check className="h-5 w-5" />
                    ) : (
                      <Icon className="h-5 w-5" />
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="font-medium">{step.name}</div>
                    <div className="text-xs opacity-75 truncate">{step.description}</div>
                  </div>
                  {isActive && (
                    <ChevronRight className="h-4 w-4 flex-shrink-0" />
                  )}
                </div>
              </button>
            );
          })}
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-auto">
        <div className="min-h-full bg-background">
          {renderStepContent()}
        </div>
      </div>
    </div>
  );
}

export default function EditAgentPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-foreground">Loading agent editor...</p>
        </div>
      </div>
    }>
      <EditAgentContent />
    </Suspense>
  );
}
