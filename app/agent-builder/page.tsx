"use client"

import { useState, useR<PERSON>, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON><PERSON>, RefreshCw, Database, AlertCircle, CheckCircle } from "lucide-react"
// import { Card } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
// import {
//   Brain,
//   MessageSquare,
//   Settings,
//   Trash2
// } from "lucide-react"
import CreateAgentButton from "@/components/agent-builder/CreateAgentButton"
import AgentSummaryModal from "@/components/agent-builder/AgentSummaryModal"
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar"
import Image from "next/image"
import voicesList from "@/components/agent-builder/retellai-voices-list.json"
import { useToast } from "@/hooks/use-toast"
import { useAgentSync } from "@/hooks/useAgentSync"
import { useAgent } from "@/contexts/AgentContext"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, AlertDescription } from "@/components/ui/alert"
import { <PERSON><PERSON><PERSON><PERSON><PERSON>on, AgentSyncNotification } from "@/components/agent-sync"

interface Agent {
  agent_id: string
  agent_name: string
  voice_id: string
  voice_model?: string
  language?: string
  response_engine?: {
    type: string
    llm_id: string
  }
  webhook_url?: string
  created_at?: string
  last_modification_timestamp?: string
  // Additional fields for UI
  description?: string
  status?: "active" | "inactive"
  knowledgeBases?: number
  totalCalls?: number
  voice?: string
  phone?: string
  editedBy?: string
  type?: string
  behavior?: string
}

interface Voice {
  voice_id: string;
  voice_name: string;
  provider: string;
  accent?: string;
  gender?: string;
  age?: string;
  avatar_url: string;
  preview_audio_url: string;
}

// Remove mock data - will fetch real data from API

// Voice avatar mapping from RetellAI voices list - map by voice_id
const voiceAvatars = voicesList.reduce((acc, voice) => {
  acc[voice.voice_id] = {
    avatarUrl: voice.avatar_url,
    initials: voice.voice_name.substring(0, 2).toUpperCase(),
    color: "bg-gray-100 text-gray-800",
    name: voice.voice_name
  }
  return acc
}, {} as Record<string, { avatarUrl: string, initials: string, color: string, name: string }>)

export default function AgentBuilderPage() {
  const router = useRouter()
  const [agents, setAgents] = useState<Agent[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedAgent, setSelectedAgent] = useState<Agent | null>(null)
  const [showAgentSummary, setShowAgentSummary] = useState(false)
  const [selectedVoiceData, setSelectedVoiceData] = useState<Voice | null>(null)
  const [phoneNumbers, setPhoneNumbers] = useState<any[]>([])
  const audioRef = useRef<HTMLAudioElement | null>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const { toast } = useToast()

  // Sync functionality
  const {
    loading: syncLoading,
    error: syncError,
    syncStatus,
    executeSync,
    getSyncStatus,
    isSyncNeeded,
    syncHealth
  } = useAgentSync()

  // Agent context for refreshing global state
  const { setAvailableAgents } = useAgent()

  // Initialize audio reference
  if (typeof window !== 'undefined' && !audioRef.current) {
    audioRef.current = new Audio();
    audioRef.current.onended = () => {
      setIsPlaying(false);
    };
  }

  // Function to fetch phone numbers from RetellAI
  const fetchPhoneNumbers = async () => {
    try {
      const response = await fetch('/api/retell/phone-numbers')
      if (!response.ok) {
        throw new Error(`Failed to fetch phone numbers: ${response.status}`)
      }
      const phoneData = await response.json()
      setPhoneNumbers(phoneData)
      return phoneData
    } catch (err) {
      console.error('Error fetching phone numbers:', err)
      return []
    }
  }

  // Function to get phone number for an agent
  const getPhoneNumberForAgent = (agentId: string, phoneNumbers: any[]) => {
    const phoneNumber = phoneNumbers.find(
      phone => phone.inbound_agent_id === agentId || phone.outbound_agent_id === agentId
    )
    return phoneNumber ? phoneNumber.phone_number_pretty || phoneNumber.phone_number : "-"
  }

  // Function to fetch agents from local database (synced with RetellAI)
  const fetchAgents = async () => {
    try {
      setLoading(true)
      setError(null)

      // Fetch both agents and phone numbers in parallel
      const [agentsResponse, phoneData] = await Promise.all([
        fetch('/api/organization/agents'),
        fetchPhoneNumbers()
      ])

      if (!agentsResponse.ok) {
        throw new Error(`Failed to fetch agents: ${agentsResponse.status}`)
      }

      const dbAgents = await agentsResponse.json()
      console.log('Fetched local agents:', dbAgents)

      // Filter only active agents and transform to match our interface
      const activeAgents = dbAgents.filter((agent: any) => agent.status === 'active')

      const transformedAgents: Agent[] = activeAgents.map((agent: any) => {
        // Get RetellAI data from settings if available
        const retellData = agent.settings?.retell_data || {}

        return {
          agent_id: agent.agent_id,
          agent_name: agent.name,
          voice_id: retellData.voice_id || '',
          voice_model: retellData.voice_model,
          language: retellData.language || 'en-US',
          response_engine: retellData.response_engine,
          webhook_url: retellData.webhook_url,
          created_at: agent.created_at,
          last_modification_timestamp: retellData.last_modification_timestamp,
          // UI fields
          description: `AI agent powered by ${retellData.voice_model || 'RetellAI'}`,
          status: "active" as const,
          knowledgeBases: 0, // TODO: Fetch from knowledge base API
          totalCalls: 0, // TODO: Fetch from analytics
          voice: getVoiceNameFromId(retellData.voice_id),
          phone: getPhoneNumberForAgent(agent.agent_id, phoneData),
          editedBy: retellData.last_modification_timestamp ?
            new Date(retellData.last_modification_timestamp).toLocaleDateString() :
            new Date(agent.created_at).toLocaleDateString(),
          type: agent.type || "RetellAI Agent",
          behavior: "AI-powered voice agent" // TODO: Extract from LLM prompt
        }
      })

      setAgents(transformedAgents)

      // Update global agent context
      const agentNames = transformedAgents.map(agent => agent.agent_name).filter(Boolean).sort()
      setAvailableAgents(agentNames)
    } catch (err) {
      console.error('Error fetching agents:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch agents')
    } finally {
      setLoading(false)
    }
  }

  // Fetch agents on component mount
  useEffect(() => {
    fetchAgents()
    getSyncStatus() // Also get sync status
  }, [getSyncStatus])

  // Handle sync with RetellAI
  const handleSyncWithRetellAI = async () => {
    try {
      const result = await executeSync()

      if (result.success) {
        // Refresh agents after successful sync
        await fetchAgents()

        toast({
          title: "Sync Completed",
          description: `Successfully synced ${result.summary.added} new agents, updated ${result.summary.updated} agents.`,
          variant: "default",
        })
      } else {
        throw new Error("Sync failed")
      }
    } catch (error) {
      console.error('Sync error:', error)
      toast({
        title: "Sync Failed",
        description: error instanceof Error ? error.message : "Failed to sync with RetellAI. Please try again.",
        variant: "destructive",
      })
    }
  }

  // Helper function to get voice name from voice ID
  const getVoiceNameFromId = (voiceId: string): string => {
    const voice = voicesList.find(v => v.voice_id === voiceId)
    return voice?.voice_name || voiceId
  }

  // Function to render voice avatar - now takes voice_id instead of voice name
  const renderVoiceAvatar = (voiceId: string, voiceName?: string) => {
    const avatar = voiceAvatars[voiceId] || {
      avatarUrl: "",
      initials: (voiceName || voiceId).substring(0, 2).toUpperCase(),
      color: "bg-gray-100 text-gray-800",
      name: voiceName || voiceId
    }

    return (
      <Avatar className="h-8 w-8 border border-gray-200 shadow-sm">
        {avatar.avatarUrl && (
          <AvatarImage
            src={avatar.avatarUrl}
            alt={avatar.name}
            className="object-cover"
          />
        )}
        <AvatarFallback className={`text-xs font-medium ${avatar.color}`}>
          {avatar.initials}
        </AvatarFallback>
      </Avatar>
    )
  }

  const handleAgentClick = (agent: Agent) => {
    setSelectedAgent(agent);

    // Find voice data if available
    if (agent.voice_id) {
      const voiceData = voicesList.find((voice: Voice) => voice.voice_id === agent.voice_id);
      setSelectedVoiceData(voiceData || null);
    } else {
      setSelectedVoiceData(null);
    }

    setShowAgentSummary(true);
  };

  const handleCloseAgentSummary = () => {
    setShowAgentSummary(false);
    setSelectedAgent(null);
    setSelectedVoiceData(null);
  };

  const handleEditAgent = () => {
    // Edit functionality is now handled within the modal itself
    // No need to navigate away or close the modal
    console.log('Edit agent:', selectedAgent);
  };

  const handleDeleteAgent = async (agentId: string) => {
    try {
      const response = await fetch(`/api/retell/agents/${agentId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete agent');
      }

      // Refresh the agents list
      await fetchAgents();

      // Show success toast
      toast({
        title: "Agent Deleted",
        description: "The AI agent has been permanently deleted.",
        variant: "default",
      });

    } catch (error) {
      console.error('Error deleting agent:', error);

      // Show error toast
      toast({
        title: "Delete Failed",
        description: error instanceof Error ? error.message : "Failed to delete agent. Please try again.",
        variant: "destructive",
      });

      throw error; // Re-throw to let the dialog handle the error display
    }
  };





  const playVoiceSample = (url: string) => {
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause();
        setIsPlaying(false);
      } else {
        audioRef.current.src = url;
        audioRef.current.play();
        setIsPlaying(true);
      }
    }
  };

  return (
    <div className="p-8 max-w-[1600px] mx-auto">
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-2xl font-semibold mb-2">AI Agent Builder</h1>
          <p className="text-muted-foreground">Create and manage your AI agents and their knowledge bases</p>
        </div>
        <div className="flex items-center gap-3">
          <AgentSyncButton
            onSyncComplete={fetchAgents}
            variant="outline"
            size="sm"
            showStatus={true}
          />
          <CreateAgentButton />
        </div>
      </div>

      {/* Sync Notification */}
      <div className="mb-6">
        <AgentSyncNotification
          onSyncClick={handleSyncWithRetellAI}
          dismissible={true}
        />
      </div>

      {/* Existing Agents */}
      <h2 className="text-xl font-semibold mb-4">Your AI Agents</h2>
      <div className="w-full mb-6">
        <div className="grid grid-cols-12 gap-4 px-6 py-3 bg-gray-50 dark:bg-gray-800 rounded-t-lg">
          <div className="col-span-4">Agent Name</div>
          <div className="col-span-2">Agent Type</div>
          <div className="col-span-2">Voice</div>
          <div className="col-span-2">Phone</div>
          <div className="col-span-2">Edited by</div>
        </div>

        {loading ? (
          <div className="px-6 py-8 text-center text-muted-foreground">
            Loading agents...
          </div>
        ) : error ? (
          <div className="px-6 py-8 text-center text-red-500">
            Error: {error}
          </div>
        ) : agents.length === 0 ? (
          <div className="px-6 py-8 text-center text-muted-foreground">
            No agents found. Create your first agent to get started.
          </div>
        ) : (
          agents.map((agent) => (
          <div
            key={agent.agent_id}
            className="grid grid-cols-12 gap-4 px-6 py-4 border-b hover:bg-gray-50 dark:hover:bg-gray-800/50 cursor-pointer"
            onClick={() => handleAgentClick(agent)}
          >
            <div className="col-span-4 flex items-center gap-3">
              <div className="p-2 rounded-full bg-primary/10">
                <Bot className="h-5 w-5 text-primary" />
              </div>
              <div>
                <h3 className="font-medium">{agent.agent_name}</h3>
                <p className="text-sm text-muted-foreground">{agent.description}</p>
              </div>
            </div>
            <div className="col-span-2 flex items-center">{agent.type}</div>
            <div className="col-span-2 flex items-center gap-2">
              {agent.voice_id && renderVoiceAvatar(agent.voice_id, agent.voice)}
              <span>{agent.voice}</span>
            </div>
            <div className="col-span-2 flex items-center">{agent.phone}</div>
            <div className="col-span-2 flex items-center justify-between">
              <span>{agent.editedBy}</span>
              <button
                className="text-gray-500 hover:text-gray-700"
                onClick={(e) => {
                  e.stopPropagation(); // Prevent row click
                  // Handle menu button click
                }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5"><circle cx="12" cy="12" r="1"/><circle cx="19" cy="12" r="1"/><circle cx="5" cy="12" r="1"/></svg>
              </button>
            </div>
          </div>
          ))
        )}
      </div>

      {/* Add New Agent Button */}
      <div className="mt-4">
        <CreateAgentButton variant="primary" />
      </div>

      {/* Agent Summary Modal */}
      <AgentSummaryModal
        isOpen={showAgentSummary}
        onClose={handleCloseAgentSummary}
        agent={selectedAgent}
        voiceData={selectedVoiceData}
        onPlayVoiceSample={playVoiceSample}
        onEditAgent={handleEditAgent}
        onDeleteAgent={handleDeleteAgent}
      />
    </div>
  )
}