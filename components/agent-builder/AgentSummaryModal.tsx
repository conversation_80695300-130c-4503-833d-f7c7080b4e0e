"use client";

import React, { useEffect, useRef, useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { X, Play, Pause, Edit, Trash2, Save, XCircle, Check } from "lucide-react"
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { useToast } from "@/hooks/use-toast"
import Image from "next/image"
import { cn } from "@/lib/utils"
import DeleteAgentDialog from "./DeleteAgentDialog"
import voicesList from "./retellai-voices-list.json"

interface Agent {
  id: string
  agent_id: string
  agent_name: string
  name: string
  description: string
  status: "active" | "inactive"
  lastModified: string
  knowledgeBases: number
  totalCalls: number
  voice?: string
  phone?: string
  editedBy?: string
  type?: string
  voiceId?: string
  voice_id?: string
  behavior?: string
  // Enhanced fields from RetellAI API
  llm_details?: {
    general_prompt?: string
    begin_message?: string
    knowledge_base_ids?: string[]
  }
  knowledge_bases?: Array<{
    knowledge_base_id: string
    knowledge_base_name: string
    knowledge_base_url?: string
  }>
  call_statistics?: {
    totalCalls: number
    totalMinutes: number
  }
}

interface Voice {
  voice_id: string;
  voice_name: string;
  provider: string;
  accent?: string;
  gender?: string;
  age?: string;
  avatar_url: string;
  preview_audio_url: string;
}

interface AgentSummaryModalProps {
  isOpen: boolean
  onClose: () => void
  agent: Agent | null
  voiceData: Voice | null
  onPlayVoiceSample?: (url: string) => void
  onEditAgent?: () => void
  onDeleteAgent?: (agentId: string) => Promise<void>
}

export default function AgentSummaryModal({
  isOpen,
  onClose,
  agent,
  voiceData,
  onPlayVoiceSample,
  onEditAgent,
  onDeleteAgent
}: AgentSummaryModalProps) {
  const { toast } = useToast()
  const overlayRef = useRef<HTMLDivElement>(null)
  const [isPlaying, setIsPlaying] = React.useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = React.useState(false)
  const [detailedAgent, setDetailedAgent] = React.useState<Agent | null>(null)
  const [isLoadingDetails, setIsLoadingDetails] = React.useState(false)
  const [isEditMode, setIsEditMode] = React.useState(false)
  const [isSaving, setIsSaving] = React.useState(false)

  // Form data for editing
  const [editForm, setEditForm] = React.useState({
    agent_name: '',
    description: '',
    type: '',
    voice_id: '',
    begin_message: '',
    general_prompt: '',
    agent_starts_conversation: false,
    knowledge_base_ids: [] as string[]
  })

  // Disable body scroll when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden"
    } else {
      document.body.style.overflow = "auto"
    }
    return () => {
      document.body.style.overflow = "auto"
    }
  }, [isOpen])

  // Close modal on escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape") onClose()
    }
    window.addEventListener("keydown", handleEscape)
    return () => window.removeEventListener("keydown", handleEscape)
  }, [onClose])

  // Close modal when clicking outside
  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === overlayRef.current) onClose()
  }

  // Fetch detailed agent data when modal opens
  useEffect(() => {
    const fetchDetailedAgent = async () => {
      if (!isOpen || !agent?.agent_id) {
        setDetailedAgent(null)
        return
      }

      setIsLoadingDetails(true)
      try {
        const response = await fetch(`/api/retell/agents/${agent.agent_id}`)
        if (!response.ok) {
          throw new Error('Failed to fetch agent details')
        }
        const detailedData = await response.json()

        // Merge the detailed data with the existing agent data
        setDetailedAgent({
          ...agent,
          ...detailedData,
          // Preserve original fields that might not be in RetellAI response
          knowledgeBases: detailedData.knowledge_bases?.length || agent.knowledgeBases || 0,
          totalCalls: detailedData.call_statistics?.totalCalls || agent.totalCalls || 0
        })
      } catch (error) {
        console.error('Error fetching detailed agent data:', error)
        // Fall back to original agent data
        setDetailedAgent(agent)
      } finally {
        setIsLoadingDetails(false)
      }
    }

    fetchDetailedAgent()
  }, [isOpen, agent])

  // Populate edit form when detailed agent data is loaded
  useEffect(() => {
    if (detailedAgent && !isEditMode) {
      setEditForm({
        agent_name: detailedAgent.agent_name || detailedAgent.name || '',
        description: detailedAgent.description || '',
        type: detailedAgent.type || 'inbound_voice',
        voice_id: detailedAgent.voice_id || detailedAgent.voiceId || '',
        begin_message: detailedAgent.llm_details?.begin_message || '',
        general_prompt: detailedAgent.llm_details?.general_prompt || detailedAgent.behavior || '',
        agent_starts_conversation: false, // This would need to be determined from agent config
        knowledge_base_ids: detailedAgent.llm_details?.knowledge_base_ids || []
      })
    }
  }, [detailedAgent, isEditMode])

  const handlePlayVoice = () => {
    if (voiceData && onPlayVoiceSample) {
      onPlayVoiceSample(voiceData.preview_audio_url)
      setIsPlaying(!isPlaying)
    }
  }

  const handleDeleteAgent = async (agentId: string) => {
    if (onDeleteAgent) {
      await onDeleteAgent(agentId)
      onClose() // Close the summary modal after successful deletion
    }
  }

  const handleDeleteClick = () => {
    setShowDeleteDialog(true)
  }

  const handleEditClick = () => {
    setIsEditMode(true)
  }

  const handleCancelEdit = () => {
    setIsEditMode(false)
    // Reset form to original values
    if (detailedAgent) {
      setEditForm({
        agent_name: detailedAgent.agent_name || detailedAgent.name || '',
        description: detailedAgent.description || '',
        type: detailedAgent.type || 'inbound_voice',
        voice_id: detailedAgent.voice_id || detailedAgent.voiceId || '',
        begin_message: detailedAgent.llm_details?.begin_message || '',
        general_prompt: detailedAgent.llm_details?.general_prompt || detailedAgent.behavior || '',
        agent_starts_conversation: false,
        knowledge_base_ids: detailedAgent.llm_details?.knowledge_base_ids || []
      })
    }
  }

  const handleSaveEdit = async () => {
    if (!detailedAgent) return

    setIsSaving(true)
    try {
      // Update agent basic info
      const agentUpdateResponse = await fetch(`/api/retell/agents/${detailedAgent.agent_id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          agent_name: editForm.agent_name,
          voice_id: editForm.voice_id,
          // Add other agent-level fields as needed
        })
      })

      if (!agentUpdateResponse.ok) {
        throw new Error('Failed to update agent')
      }

      // Update LLM configuration if it exists
      if (detailedAgent.response_engine?.llm_id) {
        const llmUpdateResponse = await fetch(`/api/retell/llms/${detailedAgent.response_engine.llm_id}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            general_prompt: editForm.general_prompt,
            begin_message: editForm.begin_message,
            knowledge_base_ids: editForm.knowledge_base_ids
          })
        })

        if (!llmUpdateResponse.ok) {
          console.warn('Failed to update LLM configuration')
        }
      }

      // Update local agent data in organization database
      const orgUpdateResponse = await fetch('/api/organization/agents', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          agent_id: detailedAgent.agent_id,
          name: editForm.agent_name,
          type: editForm.type,
          description: editForm.description
        })
      })

      if (!orgUpdateResponse.ok) {
        console.warn('Failed to update organization agent data')
      }

      toast({
        title: "Agent Updated",
        description: "Your AI agent has been successfully updated.",
        variant: "default",
      })

      setIsEditMode(false)

      // Refresh the detailed agent data
      const response = await fetch(`/api/retell/agents/${detailedAgent.agent_id}`)
      if (response.ok) {
        const updatedData = await response.json()
        setDetailedAgent({
          ...detailedAgent,
          ...updatedData,
          agent_name: editForm.agent_name,
          description: editForm.description,
          type: editForm.type
        })
      }

    } catch (error) {
      console.error('Error updating agent:', error)
      toast({
        title: "Update Failed",
        description: error instanceof Error ? error.message : "Failed to update agent. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  if (!agent) return null

  // Use detailed agent data if available, otherwise fall back to original agent
  const displayAgent = detailedAgent || agent

  return (
    <AnimatePresence>
      {isOpen && (
        <div
          ref={overlayRef}
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm"
          onClick={handleOverlayClick}
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className={cn(
              "relative w-full max-w-3xl max-h-[90vh] overflow-auto rounded-lg shadow-2xl",
              "bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800",
              "backdrop-filter backdrop-blur-xl"
            )}
          >
            {/* Header with close button */}
            <div className="sticky top-0 z-10 flex items-center justify-between border-b bg-white dark:bg-gray-900 p-4">
              <div className="flex items-center gap-2">
                <Badge
                  variant={displayAgent.status === "active" ? "default" : "secondary"}
                  className={cn(
                    "px-2 py-0.5",
                    displayAgent.status === "active" && "bg-green-500/20 text-green-600 hover:bg-green-500/30 border-green-500/10"
                  )}
                >
                  {displayAgent.status === "active" ? "Active" : "Inactive"}
                </Badge>
                <span className="text-sm text-muted-foreground">
                  Last modified: {displayAgent.lastModified}
                </span>
                {isLoadingDetails && (
                  <span className="text-xs text-muted-foreground">Loading details...</span>
                )}
              </div>
              <Button variant="ghost" size="icon" onClick={onClose}>
                <X className="h-4 w-4" />
              </Button>
            </div>

            {/* Content */}
            <div className="p-6 bg-white dark:bg-gray-900">
              {isEditMode ? (
                <div className="space-y-4 mb-6">
                  <div>
                    <Label htmlFor="agent_name">Agent Name</Label>
                    <Input
                      id="agent_name"
                      value={editForm.agent_name}
                      onChange={(e) => setEditForm(prev => ({ ...prev, agent_name: e.target.value }))}
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      value={editForm.description}
                      onChange={(e) => setEditForm(prev => ({ ...prev, description: e.target.value }))}
                      className="mt-1"
                      rows={2}
                    />
                  </div>
                </div>
              ) : (
                <>
                  <h2 className="text-2xl font-bold mb-2">{displayAgent.name}</h2>
                  <p className="text-muted-foreground mb-6">{displayAgent.description}</p>
                </>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Basic Information */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Basic Information</h3>
                  <div className="space-y-2 bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                    {isEditMode ? (
                      <div className="space-y-3">
                        <div>
                          <Label htmlFor="agent_type">Agent Type</Label>
                          <Select
                            value={editForm.type}
                            onValueChange={(value) => setEditForm(prev => ({ ...prev, type: value }))}
                          >
                            <SelectTrigger className="mt-1">
                              <SelectValue placeholder="Select agent type" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="inbound_voice">Inbound Voice</SelectItem>
                              <SelectItem value="outbound_voice">Outbound Voice</SelectItem>
                              <SelectItem value="web_call">Web Call</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Switch
                            id="agent_starts"
                            checked={editForm.agent_starts_conversation}
                            onCheckedChange={(checked) => setEditForm(prev => ({ ...prev, agent_starts_conversation: checked }))}
                          />
                          <Label htmlFor="agent_starts">Agent starts conversation</Label>
                        </div>
                      </div>
                    ) : (
                      <>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Agent Type:</span>
                          <span>{displayAgent.type}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Phone Number:</span>
                          <span>{displayAgent.phone}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Knowledge Bases:</span>
                          <span>{displayAgent.knowledgeBases}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Total Calls:</span>
                          <span>{displayAgent.call_statistics?.totalCalls || displayAgent.totalCalls}</span>
                        </div>
                      </>
                    )}
                  </div>
                </div>

                {/* Voice Information */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Voice</h3>
                  {isEditMode ? (
                    <div>
                      <Label htmlFor="voice_select">Select Voice</Label>
                      <Select
                        value={editForm.voice_id}
                        onValueChange={(value) => setEditForm(prev => ({ ...prev, voice_id: value }))}
                      >
                        <SelectTrigger className="mt-1">
                          <SelectValue placeholder="Select a voice" />
                        </SelectTrigger>
                        <SelectContent className="max-h-60">
                          {voicesList.map((voice) => (
                            <SelectItem key={voice.voice_id} value={voice.voice_id}>
                              <div className="flex items-center gap-2">
                                <Avatar className="h-6 w-6">
                                  <AvatarImage src={voice.avatar_url} alt={voice.voice_name} />
                                  <AvatarFallback className="text-xs">
                                    {voice.voice_name.substring(0, 2).toUpperCase()}
                                  </AvatarFallback>
                                </Avatar>
                                <span>{voice.voice_name}</span>
                                <span className="text-xs text-muted-foreground">
                                  ({voice.gender}, {voice.accent})
                                </span>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  ) : (
                    <>
                      {voiceData ? (
                        <div className="flex items-center gap-4 p-3 border rounded-md">
                          <Avatar className="h-12 w-12 border border-gray-200 shadow-sm">
                            <AvatarImage
                              src={voiceData.avatar_url}
                              alt={voiceData.voice_name}
                              className="object-cover"
                            />
                            <AvatarFallback className="text-sm font-medium bg-gray-100 text-gray-800">
                              {voiceData.voice_name.substring(0, 2).toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                          <div className="flex-1">
                            <div className="font-medium">{voiceData.voice_name}</div>
                            <div className="text-sm text-muted-foreground">
                              {[
                                voiceData.accent,
                                voiceData.gender,
                                voiceData.age
                              ].filter(Boolean).join(", ")}
                            </div>
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            className="flex items-center gap-1"
                            onClick={handlePlayVoice}
                          >
                            {isPlaying ? (
                              <><Pause className="h-3 w-3" /> Pause</>
                            ) : (
                              <><Play className="h-3 w-3" /> Preview</>
                            )}
                          </Button>
                        </div>
                      ) : (
                        <div className="text-muted-foreground">No voice selected</div>
                      )}
                    </>
                  )}
                </div>
              </div>

              {/* Behavior */}
              <div className="mt-6 space-y-4">
                <h3 className="text-lg font-semibold">Behavior</h3>
                <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  {isEditMode ? (
                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="general_prompt">General Behavior Prompt</Label>
                        <Textarea
                          id="general_prompt"
                          value={editForm.general_prompt}
                          onChange={(e) => setEditForm(prev => ({ ...prev, general_prompt: e.target.value }))}
                          className="mt-1"
                          rows={4}
                          placeholder="Describe how the agent should behave and respond to customers..."
                        />
                      </div>
                      <div>
                        <Label htmlFor="begin_message">Opening Message</Label>
                        <Textarea
                          id="begin_message"
                          value={editForm.begin_message}
                          onChange={(e) => setEditForm(prev => ({ ...prev, begin_message: e.target.value }))}
                          className="mt-1"
                          rows={2}
                          placeholder="Hello! How can I help you today?"
                        />
                      </div>
                    </div>
                  ) : (
                    <>
                      <p className="whitespace-pre-wrap">
                        {displayAgent.llm_details?.general_prompt || displayAgent.behavior || "No behavior defined"}
                      </p>
                      {displayAgent.llm_details?.begin_message && (
                        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                          <h4 className="text-sm font-medium text-muted-foreground mb-2">Opening Message:</h4>
                          <p className="text-sm italic">"{displayAgent.llm_details.begin_message}"</p>
                        </div>
                      )}
                    </>
                  )}
                </div>
              </div>

              {/* Knowledge Base */}
              <div className="mt-6 space-y-4">
                <h3 className="text-lg font-semibold">Knowledge Base</h3>
                <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  {isEditMode ? (
                    <div className="space-y-3">
                      <Label>Knowledge Base IDs (comma-separated)</Label>
                      <Input
                        value={editForm.knowledge_base_ids.join(', ')}
                        onChange={(e) => setEditForm(prev => ({
                          ...prev,
                          knowledge_base_ids: e.target.value.split(',').map(id => id.trim()).filter(Boolean)
                        }))}
                        placeholder="kb_id_1, kb_id_2, kb_id_3"
                        className="mt-1"
                      />
                      <p className="text-xs text-muted-foreground">
                        Enter knowledge base IDs separated by commas. Leave empty for no knowledge bases.
                      </p>
                    </div>
                  ) : (
                    <>
                      {displayAgent.knowledge_bases && displayAgent.knowledge_bases.length > 0 ? (
                        <div className="space-y-3">
                          <p className="text-sm text-muted-foreground">
                            {displayAgent.knowledge_bases.length} knowledge base(s) connected
                          </p>
                          {displayAgent.knowledge_bases.map((kb, index) => (
                            <div key={kb.knowledge_base_id} className="flex items-center justify-between p-3 bg-white dark:bg-gray-900 rounded border">
                              <div>
                                <h4 className="font-medium">{kb.knowledge_base_name}</h4>
                                <p className="text-xs text-muted-foreground">ID: {kb.knowledge_base_id}</p>
                              </div>
                              {kb.knowledge_base_url && (
                                <a
                                  href={kb.knowledge_base_url}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-primary hover:underline text-sm"
                                >
                                  View
                                </a>
                              )}
                            </div>
                          ))}
                        </div>
                      ) : (
                        <p>No knowledge bases connected</p>
                      )}
                    </>
                  )}
                </div>
              </div>

              {/* Technical Configuration */}
              <div className="mt-6 space-y-4">
                <h3 className="text-lg font-semibold">Technical Configuration</h3>
                <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Voice ID:</span>
                      <span className="font-mono text-sm">{displayAgent.voice_id || displayAgent.voiceId || "None"}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Agent ID:</span>
                      <span className="font-mono text-sm">{displayAgent.agent_id || displayAgent.id}</span>
                    </div>
                    {displayAgent.llm_details && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">LLM ID:</span>
                        <span className="font-mono text-sm">{displayAgent.response_engine?.llm_id || "None"}</span>
                      </div>
                    )}
                    {displayAgent.call_statistics && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Total Minutes:</span>
                        <span>{Math.round(displayAgent.call_statistics.totalMinutes * 100) / 100} min</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Footer with actions */}
            <div className="sticky bottom-0 z-10 flex items-center justify-between border-t bg-white dark:bg-gray-900 p-4">
              <div className="flex items-center gap-2">
                {!isEditMode && onDeleteAgent && (
                  <Button
                    variant="outline"
                    onClick={handleDeleteClick}
                    className="text-red-600 border-red-200 hover:bg-red-50 hover:border-red-300 dark:text-red-400 dark:border-red-800 dark:hover:bg-red-900/20"
                  >
                    <Trash2 className="h-4 w-4 mr-2" /> Delete Agent
                  </Button>
                )}
              </div>
              <div className="flex items-center gap-2">
                {isEditMode ? (
                  <>
                    <Button variant="outline" onClick={handleCancelEdit} disabled={isSaving}>
                      <XCircle className="h-4 w-4 mr-2" /> Cancel
                    </Button>
                    <Button onClick={handleSaveEdit} disabled={isSaving}>
                      {isSaving ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          Saving...
                        </>
                      ) : (
                        <>
                          <Save className="h-4 w-4 mr-2" /> Save Changes
                        </>
                      )}
                    </Button>
                  </>
                ) : (
                  <>
                    <Button variant="outline" onClick={onClose}>
                      Close
                    </Button>
                    <Button onClick={handleEditClick}>
                      <Edit className="h-4 w-4 mr-2" /> Edit Agent
                    </Button>
                  </>
                )}
              </div>
            </div>
          </motion.div>
        </div>
      )}

      {/* Delete Agent Dialog */}
      <DeleteAgentDialog
        isOpen={showDeleteDialog}
        onClose={() => setShowDeleteDialog(false)}
        agent={displayAgent ? {
          agent_id: displayAgent.agent_id || displayAgent.id,
          agent_name: displayAgent.agent_name || displayAgent.name,
          description: displayAgent.description,
          status: displayAgent.status,
          voice_id: displayAgent.voice_id || displayAgent.voiceId,
          totalCalls: displayAgent.call_statistics?.totalCalls || displayAgent.totalCalls,
          knowledgeBases: displayAgent.knowledgeBases
        } : null}
        onConfirmDelete={handleDeleteAgent}
      />
    </AnimatePresence>
  )
}