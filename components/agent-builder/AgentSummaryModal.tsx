"use client";

import React, { useEffect, useRef } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { X, Play, Pause, Edit, Trash2 } from "lucide-react"
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import Image from "next/image"
import { cn } from "@/lib/utils"
import DeleteAgentDialog from "./DeleteAgentDialog"

interface Agent {
  id: string
  agent_id: string
  agent_name: string
  name: string
  description: string
  status: "active" | "inactive"
  lastModified: string
  knowledgeBases: number
  totalCalls: number
  voice?: string
  phone?: string
  editedBy?: string
  type?: string
  voiceId?: string
  voice_id?: string
  behavior?: string
  // Enhanced fields from RetellAI API
  llm_details?: {
    general_prompt?: string
    begin_message?: string
    knowledge_base_ids?: string[]
  }
  knowledge_bases?: Array<{
    knowledge_base_id: string
    knowledge_base_name: string
    knowledge_base_url?: string
  }>
  call_statistics?: {
    totalCalls: number
    totalMinutes: number
  }
}

interface Voice {
  voice_id: string;
  voice_name: string;
  provider: string;
  accent?: string;
  gender?: string;
  age?: string;
  avatar_url: string;
  preview_audio_url: string;
}

interface AgentSummaryModalProps {
  isOpen: boolean
  onClose: () => void
  agent: Agent | null
  voiceData: Voice | null
  onPlayVoiceSample?: (url: string) => void
  onEditAgent?: () => void
  onDeleteAgent?: (agentId: string) => Promise<void>
}

export default function AgentSummaryModal({
  isOpen,
  onClose,
  agent,
  voiceData,
  onPlayVoiceSample,
  onEditAgent,
  onDeleteAgent
}: AgentSummaryModalProps) {
  const overlayRef = useRef<HTMLDivElement>(null)
  const [isPlaying, setIsPlaying] = React.useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = React.useState(false)
  const [detailedAgent, setDetailedAgent] = React.useState<Agent | null>(null)
  const [isLoadingDetails, setIsLoadingDetails] = React.useState(false)

  // Disable body scroll when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden"
    } else {
      document.body.style.overflow = "auto"
    }
    return () => {
      document.body.style.overflow = "auto"
    }
  }, [isOpen])

  // Close modal on escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape") onClose()
    }
    window.addEventListener("keydown", handleEscape)
    return () => window.removeEventListener("keydown", handleEscape)
  }, [onClose])

  // Close modal when clicking outside
  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === overlayRef.current) onClose()
  }

  // Fetch detailed agent data when modal opens
  useEffect(() => {
    const fetchDetailedAgent = async () => {
      if (!isOpen || !agent?.agent_id) {
        setDetailedAgent(null)
        return
      }

      setIsLoadingDetails(true)
      try {
        const response = await fetch(`/api/retell/agents/${agent.agent_id}`)
        if (!response.ok) {
          throw new Error('Failed to fetch agent details')
        }
        const detailedData = await response.json()

        // Merge the detailed data with the existing agent data
        setDetailedAgent({
          ...agent,
          ...detailedData,
          // Preserve original fields that might not be in RetellAI response
          knowledgeBases: detailedData.knowledge_bases?.length || agent.knowledgeBases || 0,
          totalCalls: detailedData.call_statistics?.totalCalls || agent.totalCalls || 0
        })
      } catch (error) {
        console.error('Error fetching detailed agent data:', error)
        // Fall back to original agent data
        setDetailedAgent(agent)
      } finally {
        setIsLoadingDetails(false)
      }
    }

    fetchDetailedAgent()
  }, [isOpen, agent])

  const handlePlayVoice = () => {
    if (voiceData && onPlayVoiceSample) {
      onPlayVoiceSample(voiceData.preview_audio_url)
      setIsPlaying(!isPlaying)
    }
  }

  const handleDeleteAgent = async (agentId: string) => {
    if (onDeleteAgent) {
      await onDeleteAgent(agentId)
      onClose() // Close the summary modal after successful deletion
    }
  }

  const handleDeleteClick = () => {
    setShowDeleteDialog(true)
  }

  if (!agent) return null

  // Use detailed agent data if available, otherwise fall back to original agent
  const displayAgent = detailedAgent || agent

  return (
    <AnimatePresence>
      {isOpen && (
        <div
          ref={overlayRef}
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm"
          onClick={handleOverlayClick}
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className={cn(
              "relative w-full max-w-3xl max-h-[90vh] overflow-auto rounded-lg shadow-2xl",
              "bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800",
              "backdrop-filter backdrop-blur-xl"
            )}
          >
            {/* Header with close button */}
            <div className="sticky top-0 z-10 flex items-center justify-between border-b bg-white dark:bg-gray-900 p-4">
              <div className="flex items-center gap-2">
                <Badge
                  variant={displayAgent.status === "active" ? "default" : "secondary"}
                  className={cn(
                    "px-2 py-0.5",
                    displayAgent.status === "active" && "bg-green-500/20 text-green-600 hover:bg-green-500/30 border-green-500/10"
                  )}
                >
                  {displayAgent.status === "active" ? "Active" : "Inactive"}
                </Badge>
                <span className="text-sm text-muted-foreground">
                  Last modified: {displayAgent.lastModified}
                </span>
                {isLoadingDetails && (
                  <span className="text-xs text-muted-foreground">Loading details...</span>
                )}
              </div>
              <Button variant="ghost" size="icon" onClick={onClose}>
                <X className="h-4 w-4" />
              </Button>
            </div>

            {/* Content */}
            <div className="p-6 bg-white dark:bg-gray-900">
              <h2 className="text-2xl font-bold mb-2">{displayAgent.name}</h2>
              <p className="text-muted-foreground mb-6">{displayAgent.description}</p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Basic Information */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Basic Information</h3>
                  <div className="space-y-2 bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Agent Type:</span>
                      <span>{displayAgent.type}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Phone Number:</span>
                      <span>{displayAgent.phone}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Knowledge Bases:</span>
                      <span>{displayAgent.knowledgeBases}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Total Calls:</span>
                      <span>{displayAgent.call_statistics?.totalCalls || displayAgent.totalCalls}</span>
                    </div>
                  </div>
                </div>

                {/* Voice Information */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Voice</h3>
                  {voiceData ? (
                    <div className="flex items-center gap-4 p-3 border rounded-md">
                      <Avatar className="h-12 w-12 border border-gray-200 shadow-sm">
                        <AvatarImage
                          src={voiceData.avatar_url}
                          alt={voiceData.voice_name}
                          className="object-cover"
                        />
                        <AvatarFallback className="text-sm font-medium bg-gray-100 text-gray-800">
                          {voiceData.voice_name.substring(0, 2).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="font-medium">{voiceData.voice_name}</div>
                        <div className="text-sm text-muted-foreground">
                          {[
                            voiceData.accent,
                            voiceData.gender,
                            voiceData.age
                          ].filter(Boolean).join(", ")}
                        </div>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex items-center gap-1"
                        onClick={handlePlayVoice}
                      >
                        {isPlaying ? (
                          <><Pause className="h-3 w-3" /> Pause</>
                        ) : (
                          <><Play className="h-3 w-3" /> Preview</>
                        )}
                      </Button>
                    </div>
                  ) : (
                    <div className="text-muted-foreground">No voice selected</div>
                  )}
                </div>
              </div>

              {/* Behavior */}
              <div className="mt-6 space-y-4">
                <h3 className="text-lg font-semibold">Behavior</h3>
                <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <p className="whitespace-pre-wrap">
                    {displayAgent.llm_details?.general_prompt || displayAgent.behavior || "No behavior defined"}
                  </p>
                  {displayAgent.llm_details?.begin_message && (
                    <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                      <h4 className="text-sm font-medium text-muted-foreground mb-2">Opening Message:</h4>
                      <p className="text-sm italic">"{displayAgent.llm_details.begin_message}"</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Knowledge Base */}
              <div className="mt-6 space-y-4">
                <h3 className="text-lg font-semibold">Knowledge Base</h3>
                <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  {displayAgent.knowledge_bases && displayAgent.knowledge_bases.length > 0 ? (
                    <div className="space-y-3">
                      <p className="text-sm text-muted-foreground">
                        {displayAgent.knowledge_bases.length} knowledge base(s) connected
                      </p>
                      {displayAgent.knowledge_bases.map((kb, index) => (
                        <div key={kb.knowledge_base_id} className="flex items-center justify-between p-3 bg-white dark:bg-gray-900 rounded border">
                          <div>
                            <h4 className="font-medium">{kb.knowledge_base_name}</h4>
                            <p className="text-xs text-muted-foreground">ID: {kb.knowledge_base_id}</p>
                          </div>
                          {kb.knowledge_base_url && (
                            <a
                              href={kb.knowledge_base_url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-primary hover:underline text-sm"
                            >
                              View
                            </a>
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p>No knowledge bases connected</p>
                  )}
                </div>
              </div>

              {/* Technical Configuration */}
              <div className="mt-6 space-y-4">
                <h3 className="text-lg font-semibold">Technical Configuration</h3>
                <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Voice ID:</span>
                      <span className="font-mono text-sm">{displayAgent.voice_id || displayAgent.voiceId || "None"}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Agent ID:</span>
                      <span className="font-mono text-sm">{displayAgent.agent_id || displayAgent.id}</span>
                    </div>
                    {displayAgent.llm_details && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">LLM ID:</span>
                        <span className="font-mono text-sm">{displayAgent.response_engine?.llm_id || "None"}</span>
                      </div>
                    )}
                    {displayAgent.call_statistics && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Total Minutes:</span>
                        <span>{Math.round(displayAgent.call_statistics.totalMinutes * 100) / 100} min</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Footer with actions */}
            <div className="sticky bottom-0 z-10 flex items-center justify-between border-t bg-white dark:bg-gray-900 p-4">
              <div className="flex items-center gap-2">
                {onDeleteAgent && (
                  <Button
                    variant="outline"
                    onClick={handleDeleteClick}
                    className="text-red-600 border-red-200 hover:bg-red-50 hover:border-red-300 dark:text-red-400 dark:border-red-800 dark:hover:bg-red-900/20"
                  >
                    <Trash2 className="h-4 w-4 mr-2" /> Delete Agent
                  </Button>
                )}
              </div>
              <div className="flex items-center gap-2">
                <Button variant="outline" onClick={onClose}>
                  Close
                </Button>
                {onEditAgent && (
                  <Button onClick={onEditAgent}>
                    <Edit className="h-4 w-4 mr-2" /> Edit Agent
                  </Button>
                )}
              </div>
            </div>
          </motion.div>
        </div>
      )}

      {/* Delete Agent Dialog */}
      <DeleteAgentDialog
        isOpen={showDeleteDialog}
        onClose={() => setShowDeleteDialog(false)}
        agent={displayAgent ? {
          agent_id: displayAgent.agent_id || displayAgent.id,
          agent_name: displayAgent.agent_name || displayAgent.name,
          description: displayAgent.description,
          status: displayAgent.status,
          voice_id: displayAgent.voice_id || displayAgent.voiceId,
          totalCalls: displayAgent.call_statistics?.totalCalls || displayAgent.totalCalls,
          knowledgeBases: displayAgent.knowledgeBases
        } : null}
        onConfirmDelete={handleDeleteAgent}
      />
    </AnimatePresence>
  )
}